"""
EduGuideBot v3 - Innovative Functions
Smart University Explorer, Financial Planning, Career Analysis, etc.
"""

import logging
from typing import Dict, Any, List, Optional
from telegram import Update, InlineKeyboardMarkup, InlineKeyboardButton
from telegram.ext import ContextTypes

from src.bot.telegram_safe_v3 import safe_edit_message, safe_answer_callback, log_telegram_errors
from src.core.data.loader import load_university_data

logger = logging.getLogger(__name__)


@log_telegram_errors
async def show_innovation_menu(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[int]:
    """Show the main innovation menu with all new features."""
    try:
        query = update.callback_query
        await safe_answer_callback(query)

        menu_text = """
🚀 <b>ការណែនាំថ្មីៗ - Innovation Hub</b>

ជ្រើសរើសមុខងារថ្មីដែលអ្នកចង់ប្រើប្រាស់:
"""

        keyboard = [
            [
                InlineKeyboardButton("🔍 ស្វែងរកសាកលវិទ្យាល័យ", callback_data="explore_universities"),
                InlineKeyboardButton("💰 ផែនការហិរញ្ញវត្ថុ", callback_data="financial_planning")
            ],
            [
                InlineKeyboardButton("🏢 វិភាគផ្លូវអាជីព", callback_data="career_analyzer"),
                InlineKeyboardButton("🏫 ជីវិតនៅបរិវេណ", callback_data="campus_life")
            ],
            [
                InlineKeyboardButton("🔬 មជ្ឈមណ្ឌលស្រាវជ្រាវ", callback_data="research_hub"),
                InlineKeyboardButton("🌐 ឱកាសអន្តរជាតិ", callback_data="international_opportunities")
            ],
            [
                InlineKeyboardButton("🔙 ត្រឡប់ទៅមេនុយដើម", callback_data="back_to_main")
            ]
        ]

        await safe_edit_message(query, menu_text, InlineKeyboardMarkup(keyboard), parse_mode='HTML')
        return None

    except Exception as e:
        logger.error(f"Error showing innovation menu: {e}", exc_info=True)
        return None


@log_telegram_errors
async def explore_universities(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[int]:
    """Smart University Explorer with advanced filters."""
    try:
        query = update.callback_query
        await safe_answer_callback(query)

        explorer_text = """
🔍 <b>ស្វែងរកសាកលវិទ្យាល័យឆ្លាតវៃ</b>

ជ្រើសរើសវិធីស្វែងរកដែលអ្នកចង់បាន:
"""

        keyboard = [
            [
                InlineKeyboardButton("🏛️ តាមប្រភេទ (សាធារណៈ/ឯកជន)", callback_data="filter_by_type"),
                InlineKeyboardButton("📅 តាមឆ្នាំបង្កើត", callback_data="filter_by_age")
            ],
            [
                InlineKeyboardButton("🌍 តាមភាគីសហការ", callback_data="filter_by_partnerships"),
                InlineKeyboardButton("🏆 តាមការទទួលស្គាល់", callback_data="filter_by_accreditation")
            ],
            [
                InlineKeyboardButton("📊 តាមគុណភាព", callback_data="filter_by_quality"),
                InlineKeyboardButton("👥 តាមអនុបាតនិស្សិត-គ្រូ", callback_data="filter_by_ratio")
            ],
            [
                InlineKeyboardButton("🔙 ត្រឡប់ទៅ Innovation Hub", callback_data="innovation_menu")
            ]
        ]

        await safe_edit_message(query, explorer_text, InlineKeyboardMarkup(keyboard), parse_mode='HTML')
        return None

    except Exception as e:
        logger.error(f"Error in university explorer: {e}", exc_info=True)
        return None


@log_telegram_errors
async def filter_universities_by_type(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[int]:
    """Filter universities by type (public/private)."""
    try:
        query = update.callback_query
        await safe_answer_callback(query)

        # Load all university data (this returns major data with university info)
        major_data = await load_university_data()

        # Extract unique universities
        universities = {}
        for major in major_data:
            uni_id = major.get('university_id', 'unknown')
            if uni_id not in universities:
                universities[uni_id] = {
                    'id': uni_id,
                    'name_kh': major.get('university_name_kh', 'មិនមានឈ្មោះ'),
                    'name': major.get('university_name_en', 'Unknown'),
                    'type': major.get('type', ''),
                    'founding_year': major.get('founding_year', ''),
                    'location': {
                        'city': major.get('city', 'មិនមានទីតាំង')
                    }
                }

        # Categorize by type
        public_unis = []
        private_unis = []

        for uni_data in universities.values():
            uni_type = uni_data.get('type', '').lower()
            if 'សាធារណៈ' in uni_type or 'public' in uni_type:
                public_unis.append(uni_data)
            elif 'ឯកជន' in uni_type or 'private' in uni_type:
                private_unis.append(uni_data)

        result_text = f"""
🏛️ <b>សាកលវិទ្យាល័យតាមប្រភេទ</b>

📊 <b>សរុប:</b> {len(universities)} សាកលវិទ្យាល័យ
🏛️ <b>សាធារណៈ:</b> {len(public_unis)} សាកលវិទ្យាល័យ
🏢 <b>ឯកជន:</b> {len(private_unis)} សាកលវិទ្យាល័យ

<b>🏛️ សាកលវិទ្យាល័យសាធារណៈ:</b>
"""
        
        for i, uni in enumerate(public_unis[:5], 1):  # Show top 5
            name = uni.get('name_kh', uni.get('name', 'មិនមានឈ្មោះ'))
            city = uni.get('location', {}).get('city', 'មិនមានទីតាំង')
            founding_year = uni.get('founding_year', '')
            year_text = f" ({founding_year})" if founding_year else ""
            result_text += f"{i}. {name} - {city}{year_text}\n"

        result_text += f"\n<b>🏢 សាកលវិទ្យាល័យឯកជន:</b>\n"
        
        for i, uni in enumerate(private_unis[:5], 1):  # Show top 5
            name = uni.get('name_kh', uni.get('name', 'មិនមានឈ្មោះ'))
            city = uni.get('location', {}).get('city', 'មិនមានទីតាំង')
            founding_year = uni.get('founding_year', '')
            year_text = f" ({founding_year})" if founding_year else ""
            result_text += f"{i}. {name} - {city}{year_text}\n"

        keyboard = [
            [
                InlineKeyboardButton("🏛️ មើលសាធារណៈទាំងអស់", callback_data="view_all_public"),
                InlineKeyboardButton("🏢 មើលឯកជនទាំងអស់", callback_data="view_all_private")
            ],
            [
                InlineKeyboardButton("🔙 ត្រឡប់ទៅ Explorer", callback_data="explore_universities")
            ]
        ]

        await safe_edit_message(query, result_text, InlineKeyboardMarkup(keyboard), parse_mode='HTML')
        return None

    except Exception as e:
        logger.error(f"Error filtering by type: {e}", exc_info=True)
        return None


@log_telegram_errors
async def financial_planning_assistant(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[int]:
    """Financial Planning Assistant with cost analysis."""
    try:
        query = update.callback_query
        await safe_answer_callback(query)

        planning_text = """
💰 <b>ជំនួយការផែនការហិរញ្ញវត្ថុ</b>

ជ្រើសរើសសេវាកម្មដែលអ្នកត្រូវការ:
"""

        keyboard = [
            [
                InlineKeyboardButton("📊 ប្រៀបធៀបថ្លៃសិក្សា", callback_data="compare_tuition"),
                InlineKeyboardButton("🎓 រកអាហារូបករណ៍", callback_data="find_scholarships")
            ],
            [
                InlineKeyboardButton("💳 គណនាចំណាយសរុប", callback_data="calculate_total_cost"),
                InlineKeyboardButton("📈 គណនា ROI", callback_data="calculate_roi")
            ],
            [
                InlineKeyboardButton("💡 ប្រឹក្សាហិរញ្ញវត្ថុ", callback_data="financial_advice"),
                InlineKeyboardButton("📋 ផែនការបង់ប្រាក់", callback_data="payment_plans")
            ],
            [
                InlineKeyboardButton("🔙 ត្រឡប់ទៅ Innovation Hub", callback_data="innovation_menu")
            ]
        ]

        await safe_edit_message(query, planning_text, InlineKeyboardMarkup(keyboard), parse_mode='HTML')
        return None

    except Exception as e:
        logger.error(f"Error in financial planning: {e}", exc_info=True)
        return None


@log_telegram_errors
async def career_path_analyzer(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[int]:
    """Career Path Analyzer using employment data."""
    try:
        query = update.callback_query
        await safe_answer_callback(query)

        career_text = """
🏢 <b>វិភាគផ្លូវអាជីព</b>

ស្វែងយល់អំពីឱកាសការងារនាពេលអនាគត:
"""

        keyboard = [
            [
                InlineKeyboardButton("📈 អត្រារកការងារ", callback_data="employment_rates"),
                InlineKeyboardButton("💼 ក្រុមហ៊ុនធំៗ", callback_data="top_employers")
            ],
            [
                InlineKeyboardButton("💰 ប្រាក់ខែមធ្យម", callback_data="salary_analysis"),
                InlineKeyboardButton("🌟 អតីតនិស្សិតល្បី", callback_data="alumni_success")
            ],
            [
                InlineKeyboardButton("📊 ទីផ្សារការងារ", callback_data="job_market"),
                InlineKeyboardButton("🔮 ការព្យាករណ៍", callback_data="career_forecast")
            ],
            [
                InlineKeyboardButton("🔙 ត្រឡប់ទៅ Innovation Hub", callback_data="innovation_menu")
            ]
        ]

        await safe_edit_message(query, career_text, InlineKeyboardMarkup(keyboard), parse_mode='HTML')
        return None

    except Exception as e:
        logger.error(f"Error in career analyzer: {e}", exc_info=True)
        return None


def register_innovation_handlers(application):
    """Register all innovation handlers."""
    from telegram.ext import CallbackQueryHandler
    
    application.add_handler(CallbackQueryHandler(show_innovation_menu, pattern="^innovation_menu$"))
    application.add_handler(CallbackQueryHandler(explore_universities, pattern="^explore_universities$"))
    application.add_handler(CallbackQueryHandler(filter_universities_by_type, pattern="^filter_by_type$"))
    application.add_handler(CallbackQueryHandler(financial_planning_assistant, pattern="^financial_planning$"))
    application.add_handler(CallbackQueryHandler(career_path_analyzer, pattern="^career_analyzer$"))
